@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Mapbox GL CSS */
@import 'mapbox-gl/dist/mapbox-gl.css';

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-lg bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-lg bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }

  .badge-info {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }
}

/* Base styles */
@layer base {
  html {
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  /* Focus styles */
  *:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  /* Selection styles */
  ::selection {
    background-color: rgb(59 130 246 / 0.3);
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Remove default button styles */
  button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
  }

  /* Custom loading spinner */
  .spinner {
    @apply inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite];
  }

  /* Map container styles */
  .mapboxgl-popup {
    max-width: 300px;
  }

  .mapboxgl-popup-content {
    @apply rounded-lg shadow-lg;
  }

  .mapboxgl-popup-close-button {
    @apply text-gray-400 hover:text-gray-600;
  }

  /* Custom map controls */
  .mapboxgl-ctrl-group {
    @apply rounded-lg shadow-lg;
  }

  .mapboxgl-ctrl button {
    @apply hover:bg-gray-50;
  }

  /* Property card hover effects */
  .property-card {
    @apply transition-all duration-200 hover:shadow-medium hover:-translate-y-1;
  }

  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Custom checkbox styles */
  .checkbox {
    @apply h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500;
  }

  /* Custom radio styles */
  .radio {
    @apply h-4 w-4 border-gray-300 text-primary-600 focus:ring-primary-500;
  }

  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
  }

  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  .table tr:hover {
    @apply bg-gray-50;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here when needed */
}
