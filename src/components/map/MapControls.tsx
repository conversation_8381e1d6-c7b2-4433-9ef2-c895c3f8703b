'use client';

import {
  AdjustmentsHorizontalIcon,
  MapIcon,
  EyeIcon,
  EyeSlashIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

interface MapControlsProps {
  mapStyle: string;
  onMapStyleChange: (style: string) => void;
  showFilters: boolean;
  onToggleFilters: () => void;
}

const mapStyles = [
  { id: 'satellite', name: 'Satellite', icon: '🛰️' },
  { id: 'streets', name: 'Streets', icon: '🗺️' },
  { id: 'outdoors', name: 'Outdoors', icon: '🏞️' },
  { id: 'light', name: 'Light', icon: '☀️' },
  { id: 'dark', name: 'Dark', icon: '🌙' },
];

export function MapControls({
  mapStyle,
  onMapStyleChange,
  showFilters,
  onToggleFilters,
}: MapControlsProps) {
  return (
    <div className="flex items-center space-x-4">
      {/* Map Style Selector */}
      <div className="flex items-center space-x-2">
        <MapIcon className="h-5 w-5 text-gray-500" />
        <select
          value={mapStyle}
          onChange={(e) => onMapStyleChange(e.target.value)}
          className="input-field text-sm"
        >
          {mapStyles.map((style) => (
            <option key={style.id} value={style.id}>
              {style.icon} {style.name}
            </option>
          ))}
        </select>
      </div>

      {/* Toggle Filters Button */}
      <button
        onClick={onToggleFilters}
        className={clsx(
          'btn-secondary flex items-center',
          showFilters && 'bg-primary-50 border-primary-200 text-primary-700'
        )}
      >
        <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
        {showFilters ? 'Hide Filters' : 'Show Filters'}
      </button>
    </div>
  );
}
