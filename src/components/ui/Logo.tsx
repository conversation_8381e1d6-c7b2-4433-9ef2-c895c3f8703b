'use client';

import Image from 'next/image';
import { clsx } from 'clsx';

interface LogoProps {
  className?: string;
  showText?: boolean;
  variant?: 'default' | 'white' | 'dark';
}

export function Logo({ className, showText = true, variant = 'default' }: LogoProps) {
  return (
    <div className={clsx('flex items-center', className)}>
      {/* Logo SVG */}
      <div className="flex-shrink-0">
        <Image
          src="/logo-one.svg"
          alt="PropBolt Brain"
          width={32}
          height={32}
          className="h-8 w-8"
          priority
        />
      </div>
      
      {/* Logo Text */}
      {showText && (
        <div className="ml-3">
          <div className={clsx(
            'text-xl font-bold tracking-tight',
            variant === 'white' && 'text-white',
            variant === 'dark' && 'text-gray-900',
            variant === 'default' && 'text-gray-900'
          )}>
            PropBolt
          </div>
          <div className={clsx(
            'text-xs font-medium tracking-wide',
            variant === 'white' && 'text-gray-200',
            variant === 'dark' && 'text-gray-600',
            variant === 'default' && 'text-primary-600'
          )}>
            BRAIN
          </div>
        </div>
      )}
    </div>
  );
}

// Alternative inline SVG version if the image doesn't load
export function LogoSVG({ className, showText = true, variant = 'default' }: LogoProps) {
  return (
    <div className={clsx('flex items-center', className)}>
      {/* Inline SVG fallback */}
      <div className="flex-shrink-0">
        <svg
          className="h-8 w-8"
          viewBox="0 0 32 32"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="32" height="32" rx="6" fill="#0ea5e9" />
          <path
            d="M8 12h16v8H8z"
            fill="white"
            fillOpacity="0.9"
          />
          <path
            d="M10 14h4v4h-4zM18 14h4v4h-4z"
            fill="#0ea5e9"
          />
          <circle cx="12" cy="16" r="1" fill="white" />
          <circle cx="20" cy="16" r="1" fill="white" />
        </svg>
      </div>
      
      {/* Logo Text */}
      {showText && (
        <div className="ml-3">
          <div className={clsx(
            'text-xl font-bold tracking-tight',
            variant === 'white' && 'text-white',
            variant === 'dark' && 'text-gray-900',
            variant === 'default' && 'text-gray-900'
          )}>
            PropBolt
          </div>
          <div className={clsx(
            'text-xs font-medium tracking-wide',
            variant === 'white' && 'text-gray-200',
            variant === 'dark' && 'text-gray-600',
            variant === 'default' && 'text-primary-600'
          )}>
            BRAIN
          </div>
        </div>
      )}
    </div>
  );
}
