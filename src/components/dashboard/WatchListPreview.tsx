'use client';

import Link from 'next/link';
import { format } from 'date-fns';
import {
  BookmarkIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  MinusIcon,
} from '@heroicons/react/24/outline';

// Mock watch list data
const watchListProperties = [
  {
    id: 1,
    address: '1234 Beachside Blvd, Daytona Beach, FL',
    price: 75000,
    originalPrice: 85000,
    priceChange: -10000,
    priceChangePercent: -11.8,
    lotSize: 2.8,
    addedDate: new Date('2024-01-10'),
    lastPriceUpdate: new Date('2024-01-14'),
    alerts: 2,
    status: 'price_drop',
  },
  {
    id: 2,
    address: '5678 River Rd, New Smyrna Beach, FL',
    price: 125000,
    originalPrice: 125000,
    priceChange: 0,
    priceChangePercent: 0,
    lotSize: 4.5,
    addedDate: new Date('2024-01-08'),
    lastPriceUpdate: new Date('2024-01-08'),
    alerts: 0,
    status: 'stable',
  },
  {
    id: 3,
    address: '9012 Country Lane, DeLand, FL',
    price: 95000,
    originalPrice: 89000,
    priceChange: 6000,
    priceChangePercent: 6.7,
    lotSize: 6.2,
    addedDate: new Date('2024-01-05'),
    lastPriceUpdate: new Date('2024-01-12'),
    alerts: 1,
    status: 'price_increase',
  },
];

export function WatchListPreview() {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return TrendingUpIcon;
    if (change < 0) return TrendingDownIcon;
    return MinusIcon;
  };

  const getPriceChangeColor = (change: number) => {
    if (change > 0) return 'text-red-600';
    if (change < 0) return 'text-green-600';
    return 'text-gray-500';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'price_drop':
        return 'badge-success';
      case 'price_increase':
        return 'badge-warning';
      case 'stable':
        return 'badge-gray';
      default:
        return 'badge-gray';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'price_drop':
        return 'Price Drop';
      case 'price_increase':
        return 'Price Increase';
      case 'stable':
        return 'Stable';
      default:
        return 'Unknown';
    }
  };

  const totalAlerts = watchListProperties.reduce((sum, prop) => sum + prop.alerts, 0);

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BookmarkIcon className="h-5 w-5 text-primary-500 mr-2" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Watch List</h3>
              <p className="text-sm text-gray-500">
                {watchListProperties.length} properties • {totalAlerts} alerts
              </p>
            </div>
          </div>
          <Link
            href="/watchlist"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View all →
          </Link>
        </div>
      </div>
      <div className="card-body">
        <div className="space-y-4">
          {watchListProperties.map((property) => {
            const PriceIcon = getPriceChangeIcon(property.priceChange);
            
            return (
              <div
                key={property.id}
                className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors duration-200"
              >
                {/* Header */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 mb-1">
                      {property.address}
                    </h4>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">{property.lotSize} acres</span>
                      <span className={`badge ${getStatusBadge(property.status)}`}>
                        {getStatusText(property.status)}
                      </span>
                      {property.alerts > 0 && (
                        <span className="badge-error">
                          {property.alerts} alert{property.alerts > 1 ? 's' : ''}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Price Information */}
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <div className="text-lg font-bold text-gray-900">
                      {formatPrice(property.price)}
                    </div>
                    {property.priceChange !== 0 && (
                      <div className={`flex items-center text-sm ${getPriceChangeColor(property.priceChange)}`}>
                        <PriceIcon className="h-3 w-3 mr-1" />
                        {formatPrice(Math.abs(property.priceChange))} 
                        ({property.priceChangePercent > 0 ? '+' : ''}{property.priceChangePercent.toFixed(1)}%)
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-gray-500">Added</div>
                    <div className="text-sm font-medium text-gray-700">
                      {format(property.addedDate, 'MMM d')}
                    </div>
                  </div>
                </div>

                {/* Last Update */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>
                    Last updated: {format(property.lastPriceUpdate, 'MMM d, h:mm a')}
                  </span>
                  <Link
                    href={`/property/${property.id}`}
                    className="text-primary-600 hover:text-primary-700 font-medium"
                  >
                    View →
                  </Link>
                </div>
              </div>
            );
          })}
        </div>

        {watchListProperties.length === 0 && (
          <div className="text-center py-8">
            <BookmarkIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-sm font-medium text-gray-900 mb-2">No properties in watch list</h3>
            <p className="text-sm text-gray-500 mb-4">
              Add properties to your watch list to track price changes and get alerts
            </p>
            <Link href="/search" className="btn-primary">
              Find Properties
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
