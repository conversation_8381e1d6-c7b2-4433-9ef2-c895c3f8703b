'use client';

import {
  HomeIcon,
  MapIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

const stats = [
  {
    name: 'Total Properties',
    value: '2,847',
    change: '+12%',
    changeType: 'increase',
    icon: HomeIcon,
    description: 'Vacant land listings',
  },
  {
    name: 'Avg. Price/Acre',
    value: '$8,450',
    change: '+5.2%',
    changeType: 'increase',
    icon: CurrencyDollarIcon,
    description: 'Market average',
  },
  {
    name: 'Active Searches',
    value: '156',
    change: '+23%',
    changeType: 'increase',
    icon: MapIcon,
    description: 'User searches',
  },
  {
    name: 'Market Activity',
    value: '89%',
    change: '-2.1%',
    changeType: 'decrease',
    icon: ChartBarIcon,
    description: 'vs. last month',
  },
];

export function StatsOverview() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => (
        <div key={stat.name} className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-primary-100 rounded-lg">
                  <stat.icon className="h-6 w-6 text-primary-600" aria-hidden="true" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className="flex items-center">
                    {stat.changeType === 'increase' ? (
                      <TrendingUpIcon className="h-4 w-4 text-success-500 mr-1" />
                    ) : (
                      <TrendingDownIcon className="h-4 w-4 text-error-500 mr-1" />
                    )}
                    <span
                      className={clsx(
                        'text-sm font-medium',
                        stat.changeType === 'increase' ? 'text-success-600' : 'text-error-600'
                      )}
                    >
                      {stat.change}
                    </span>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
