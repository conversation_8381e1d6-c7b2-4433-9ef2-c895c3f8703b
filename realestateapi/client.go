package realestateapi

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

// Client represents the RealEstateAPI.com client
type Client struct {
	APIKey  string
	BaseURL string
	client  *http.Client
}

// NewClient creates a new RealEstateAPI client
func NewClient() *Client {
	apiKey := os.Getenv("REAL_ESTATE_API_KEY")
	if apiKey == "" {
		panic("REAL_ESTATE_API_KEY environment variable is required")
	}

	baseURL := os.Getenv("REAL_ESTATE_API_URL")
	if baseURL == "" {
		baseURL = "https://api.realestateapi.com/v2"
	}

	return &Client{
		APIKey:  apiKey,
		BaseURL: baseURL,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// makeRequest makes an HTTP request to the RealEstateAPI
func (c *Client) makeRequest(method, endpoint string, payload interface{}) ([]byte, error) {
	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("error marshaling request payload: %v", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	url := c.BaseURL + endpoint
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", c.APIKey)
	req.Header.Set("User-Agent", "PropBolt-Brain/1.0")

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	return responseBody, nil
}

// AutoCompleteRequest represents the request structure for AutoComplete API
type AutoCompleteRequest struct {
	Input string `json:"input"`
}

// AutoCompleteResponse represents the response structure for AutoComplete API
type AutoCompleteResponse struct {
	Results []AutoCompleteResult `json:"results"`
}

type AutoCompleteResult struct {
	SearchType  string `json:"searchType"`
	DisplayText string `json:"displayText"`
	Address     string `json:"address,omitempty"`
	City        string `json:"city,omitempty"`
	State       string `json:"state,omitempty"`
	ZipCode     string `json:"zipCode,omitempty"`
	RegionID    int    `json:"regionId,omitempty"`
}

// AutoComplete calls the AutoComplete API
func (c *Client) AutoComplete(input string) (*AutoCompleteResponse, error) {
	payload := AutoCompleteRequest{
		Input: input,
	}

	responseBody, err := c.makeRequest("POST", "/AutoComplete", payload)
	if err != nil {
		return nil, err
	}

	var response AutoCompleteResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// PropertyMappingRequest represents the request structure for Property Mapping API
type PropertyMappingRequest struct {
	Query interface{} `json:"query"`
}

// PropertyMappingResponse represents the response structure for Property Mapping API
type PropertyMappingResponse struct {
	Properties []PropertyPin `json:"properties"`
	Total      int           `json:"total"`
}

type PropertyPin struct {
	ID        string  `json:"id"`
	Address   string  `json:"address"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Price     float64 `json:"price,omitempty"`
	Status    string  `json:"status,omitempty"`
}

// PropertyMapping calls the Property Mapping API
func (c *Client) PropertyMapping(query interface{}) (*PropertyMappingResponse, error) {
	payload := PropertyMappingRequest{
		Query: query,
	}

	responseBody, err := c.makeRequest("POST", "/PropertyMapping", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyMappingResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// PropertyDetailRequest represents the request structure for Property Detail API
type PropertyDetailRequest struct {
	Address    string `json:"address,omitempty"`
	PropertyID string `json:"propertyId,omitempty"`
	Comps      bool   `json:"comps,omitempty"`
}

// PropertyDetailResponse represents the response structure for Property Detail API
type PropertyDetailResponse struct {
	Property PropertyDetail `json:"property"`
}

type PropertyDetail struct {
	ID           string                 `json:"id"`
	Address      string                 `json:"address"`
	City         string                 `json:"city"`
	State        string                 `json:"state"`
	ZipCode      string                 `json:"zipCode"`
	Latitude     float64                `json:"latitude"`
	Longitude    float64                `json:"longitude"`
	PropertyInfo map[string]interface{} `json:"propertyInfo"`
	OwnerInfo    map[string]interface{} `json:"ownerInfo"`
	LotInfo      map[string]interface{} `json:"lotInfo"`
	TaxInfo      map[string]interface{} `json:"taxInfo"`
	SaleHistory  []map[string]interface{} `json:"saleHistory"`
	MLSHistory   []map[string]interface{} `json:"mlsHistory"`
	Comps        []map[string]interface{} `json:"comps,omitempty"`
}

// PropertyDetail calls the Property Detail API
func (c *Client) PropertyDetail(address, propertyID string, includeComps bool) (*PropertyDetailResponse, error) {
	payload := PropertyDetailRequest{
		Address:    address,
		PropertyID: propertyID,
		Comps:      includeComps,
	}

	responseBody, err := c.makeRequest("POST", "/PropertyDetail", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyDetailResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// PropertyDetailBulkRequest represents the request structure for Property Detail Bulk API
type PropertyDetailBulkRequest struct {
	PropertyIDs []string `json:"propertyIds"`
	Comps       bool     `json:"comps,omitempty"`
}

// PropertyDetailBulkResponse represents the response structure for Property Detail Bulk API
type PropertyDetailBulkResponse struct {
	Properties []PropertyDetail `json:"properties"`
}

// PropertyDetailBulk calls the Property Detail Bulk API
func (c *Client) PropertyDetailBulk(propertyIDs []string, includeComps bool) (*PropertyDetailBulkResponse, error) {
	if len(propertyIDs) > 1000 {
		return nil, fmt.Errorf("maximum 1000 property IDs allowed per request")
	}

	payload := PropertyDetailBulkRequest{
		PropertyIDs: propertyIDs,
		Comps:       includeComps,
	}

	responseBody, err := c.makeRequest("POST", "/PropertyDetailBulk", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyDetailBulkResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// PropertySearchRequest represents the request structure for Property Search API
type PropertySearchRequest struct {
	Query   interface{} `json:"query"`
	Limit   int         `json:"limit,omitempty"`
	Offset  int         `json:"offset,omitempty"`
	IDsOnly bool        `json:"ids_only,omitempty"`
}

// PropertySearchResponse represents the response structure for Property Search API
type PropertySearchResponse struct {
	Properties []PropertySearchResult `json:"properties"`
	Total      int                    `json:"total"`
	Offset     int                    `json:"offset"`
}

type PropertySearchResult struct {
	ID           string                 `json:"id"`
	Address      string                 `json:"address"`
	City         string                 `json:"city"`
	State        string                 `json:"state"`
	ZipCode      string                 `json:"zipCode"`
	Latitude     float64                `json:"latitude"`
	Longitude    float64                `json:"longitude"`
	Price        float64                `json:"price,omitempty"`
	PropertyInfo map[string]interface{} `json:"propertyInfo,omitempty"`
}

// PropertySearch calls the Property Search API
func (c *Client) PropertySearch(query interface{}, limit, offset int, idsOnly bool) (*PropertySearchResponse, error) {
	payload := PropertySearchRequest{
		Query:   query,
		Limit:   limit,
		Offset:  offset,
		IDsOnly: idsOnly,
	}

	responseBody, err := c.makeRequest("POST", "/PropertySearch", payload)
	if err != nil {
		return nil, err
	}

	var response PropertySearchResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// InvoluntaryLiensRequest represents the request structure for Involuntary Liens API
type InvoluntaryLiensRequest struct {
	Address    string `json:"address,omitempty"`
	PropertyID string `json:"propertyId,omitempty"`
}

// InvoluntaryLiensResponse represents the response structure for Involuntary Liens API
type InvoluntaryLiensResponse struct {
	Liens []LienRecord `json:"liens"`
}

type LienRecord struct {
	Type        string  `json:"type"`
	Amount      float64 `json:"amount"`
	Date        string  `json:"date"`
	Description string  `json:"description"`
	Status      string  `json:"status"`
}

// InvoluntaryLiens calls the Involuntary Liens API
func (c *Client) InvoluntaryLiens(address, propertyID string) (*InvoluntaryLiensResponse, error) {
	payload := InvoluntaryLiensRequest{
		Address:    address,
		PropertyID: propertyID,
	}

	responseBody, err := c.makeRequest("POST", "/InvoluntaryLiens", payload)
	if err != nil {
		return nil, err
	}

	var response InvoluntaryLiensResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// PropertyCompsV3Request represents the request structure for Property Comps v3 API
type PropertyCompsV3Request struct {
	Address           string                 `json:"address"`
	CustomParameters  map[string]interface{} `json:"customParameters,omitempty"`
	BoostParameters   map[string]interface{} `json:"boostParameters,omitempty"`
	MaxComps          int                    `json:"maxComps,omitempty"`
	RadiusMiles       float64                `json:"radiusMiles,omitempty"`
}

// PropertyCompsV3Response represents the response structure for Property Comps v3 API
type PropertyCompsV3Response struct {
	Subject PropertyCompSubject `json:"subject"`
	Comps   []PropertyComp      `json:"comps"`
	AVM     AVMRange            `json:"avm"`
}

type PropertyCompSubject struct {
	Address      string  `json:"address"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	LivingSqft   int     `json:"livingSqft"`
	LotSqft      int     `json:"lotSqft"`
	Bedrooms     int     `json:"bedrooms"`
	Bathrooms    float64 `json:"bathrooms"`
	YearBuilt    int     `json:"yearBuilt"`
}

type PropertyComp struct {
	Address       string  `json:"address"`
	Distance      float64 `json:"distance"`
	SalePrice     float64 `json:"salePrice"`
	SaleDate      string  `json:"saleDate"`
	LivingSqft    int     `json:"livingSqft"`
	LotSqft       int     `json:"lotSqft"`
	Bedrooms      int     `json:"bedrooms"`
	Bathrooms     float64 `json:"bathrooms"`
	YearBuilt     int     `json:"yearBuilt"`
	PricePerSqft  float64 `json:"pricePerSqft"`
	SimilarityScore float64 `json:"similarityScore"`
}

type AVMRange struct {
	Low        float64 `json:"low"`
	High       float64 `json:"high"`
	Confidence float64 `json:"confidence"`
}

// PropertyCompsV3 calls the Property Comps v3 API
func (c *Client) PropertyCompsV3(address string, customParams, boostParams map[string]interface{}, maxComps int, radiusMiles float64) (*PropertyCompsV3Response, error) {
	payload := PropertyCompsV3Request{
		Address:           address,
		CustomParameters:  customParams,
		BoostParameters:   boostParams,
		MaxComps:          maxComps,
		RadiusMiles:       radiusMiles,
	}

	responseBody, err := c.makeRequest("POST", "/v3/PropertyComps", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyCompsV3Response
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// PropertyCompsV2Request represents the request structure for Property Comps v2 API
type PropertyCompsV2Request struct {
	Address     string  `json:"address"`
	MaxComps    int     `json:"maxComps,omitempty"`
	RadiusMiles float64 `json:"radiusMiles,omitempty"`
}

// PropertyCompsV2Response represents the response structure for Property Comps v2 API
type PropertyCompsV2Response struct {
	Subject PropertyCompSubject `json:"subject"`
	Comps   []PropertyComp      `json:"comps"`
	AVM     float64             `json:"avm"`
}

// PropertyCompsV2 calls the Property Comps v2 API
func (c *Client) PropertyCompsV2(address string, maxComps int, radiusMiles float64) (*PropertyCompsV2Response, error) {
	payload := PropertyCompsV2Request{
		Address:     address,
		MaxComps:    maxComps,
		RadiusMiles: radiusMiles,
	}

	responseBody, err := c.makeRequest("POST", "/v2/PropertyComps", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyCompsV2Response
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

// SkipTraceRequest represents the request structure for SkipTrace API
type SkipTraceRequest struct {
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Address   string `json:"address"`
	City      string `json:"city"`
	State     string `json:"state"`
	Zip       string `json:"zip"`
}

// SkipTraceResponse represents the response structure for SkipTrace API
type SkipTraceResponse struct {
	Identity     SkipTraceIdentity     `json:"identity"`
	Demographics SkipTraceDemographics `json:"demographics"`
	Stats        SkipTraceStats        `json:"stats"`
}

type SkipTraceIdentity struct {
	Name            string                   `json:"name"`
	AddressHistory  []map[string]interface{} `json:"addressHistory"`
	MailingAddress  map[string]interface{}   `json:"mailingAddress"`
	PhoneNumbers    []string                 `json:"phoneNumbers"`
	EmailAddresses  []string                 `json:"emailAddresses"`
}

type SkipTraceDemographics struct {
	Age       int    `json:"age"`
	Gender    string `json:"gender"`
	Birthday  string `json:"birthday"`
	Education string `json:"education"`
	Jobs      []map[string]interface{} `json:"jobs"`
}

type SkipTraceStats struct {
	PhonesFound        int `json:"phonesFound"`
	EmailsFound        int `json:"emailsFound"`
	JobsFound          int `json:"jobsFound"`
	RelationshipsFound int `json:"relationshipsFound"`
}

// SkipTrace calls the SkipTrace API
func (c *Client) SkipTrace(firstName, lastName, address, city, state, zip string) (*SkipTraceResponse, error) {
	payload := SkipTraceRequest{
		FirstName: firstName,
		LastName:  lastName,
		Address:   address,
		City:      city,
		State:     state,
		Zip:       zip,
	}

	// SkipTrace API uses v1 endpoint
	responseBody, err := c.makeRequest("POST", "/v1/SkipTrace", payload)
	if err != nil {
		return nil, err
	}

	var response SkipTraceResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}
