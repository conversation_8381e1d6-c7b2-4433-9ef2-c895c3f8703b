'use client';

import { useState, useEffect, useCallback } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { MapView } from '@/components/map/MapView';
import { MapFilters } from '@/components/map/MapFilters';
import { PropertyDetails } from '@/components/map/PropertyDetails';
import { MapControls } from '@/components/map/MapControls';
import { usePropertyData } from '@/hooks/usePropertyData';

export default function MapPage() {
  const [selectedProperty, setSelectedProperty] = useState<any>(null);
  const [filters, setFilters] = useState({
    priceRange: [0, 1000000],
    lotSizeRange: [0, 50],
    zoning: [],
    features: [],
    location: '',
  });
  const [mapStyle, setMapStyle] = useState('satellite');
  const [showFilters, setShowFilters] = useState(true);

  // Fetch property data with filters
  const { data: properties, isLoading, error } = usePropertyData(filters);

  const handlePropertySelect = useCallback((property: any) => {
    setSelectedProperty(property);
  }, []);

  const handleFilterChange = useCallback((newFilters: any) => {
    setFilters(newFilters);
  }, []);

  const handleMapStyleChange = useCallback((style: string) => {
    setMapStyle(style);
  }, []);

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Property Map</h1>
            <p className="text-gray-600">
              Explore vacant land properties with interactive mapping
            </p>
          </div>
          
          <MapControls
            mapStyle={mapStyle}
            onMapStyleChange={handleMapStyleChange}
            showFilters={showFilters}
            onToggleFilters={() => setShowFilters(!showFilters)}
          />
        </div>

        {/* Map Container */}
        <div className="flex-1 flex gap-6 min-h-0">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-80 flex-shrink-0">
              <MapFilters
                filters={filters}
                onFiltersChange={handleFilterChange}
                propertyCount={properties?.length || 0}
                isLoading={isLoading}
              />
            </div>
          )}

          {/* Map View */}
          <div className="flex-1 relative">
            <MapView
              properties={properties || []}
              selectedProperty={selectedProperty}
              onPropertySelect={handlePropertySelect}
              mapStyle={mapStyle}
              isLoading={isLoading}
              error={error}
            />
          </div>

          {/* Property Details Panel */}
          {selectedProperty && (
            <div className="w-96 flex-shrink-0">
              <PropertyDetails
                property={selectedProperty}
                onClose={() => setSelectedProperty(null)}
              />
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
