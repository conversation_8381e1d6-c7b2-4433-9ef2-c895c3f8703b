'use client';

import { useQuery } from '@tanstack/react-query';
import { propertyApi } from '@/lib/api/propertyApi';

interface PropertyFilters {
  priceRange: [number, number];
  lotSizeRange: [number, number];
  zoning: string[];
  features: string[];
  location: string;
}

export function usePropertyData(filters: PropertyFilters) {
  return useQuery({
    queryKey: ['properties', filters],
    queryFn: () => propertyApi.searchProperties(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
}

export function usePropertyDetails(propertyId: string) {
  return useQuery({
    queryKey: ['property', propertyId],
    queryFn: () => propertyApi.getPropertyDetails(propertyId),
    enabled: !!propertyId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function usePropertyComparables(propertyId: string) {
  return useQuery({
    queryKey: ['property-comparables', propertyId],
    queryFn: () => propertyApi.getPropertyComparables(propertyId),
    enabled: !!propertyId,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
}

export function usePropertyHistory(propertyId: string) {
  return useQuery({
    queryKey: ['property-history', propertyId],
    queryFn: () => propertyApi.getPropertyHistory(propertyId),
    enabled: !!propertyId,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

export function useMarketAnalytics(location: string) {
  return useQuery({
    queryKey: ['market-analytics', location],
    queryFn: () => propertyApi.getMarketAnalytics(location),
    enabled: !!location,
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}
