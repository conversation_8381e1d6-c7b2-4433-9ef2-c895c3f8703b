'use client';

import { useState } from 'react';
import Image from 'next/image';
import {
  XMarkIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  HomeIcon,
  CalendarIcon,
  BookmarkIcon,
  ShareIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';

interface PropertyDetailsProps {
  property: any;
  onClose: () => void;
}

export function PropertyDetails({ property, onClose }: PropertyDetailsProps) {
  const [isWatched, setIsWatched] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'details', name: 'Details' },
    { id: 'analysis', name: 'Analysis' },
  ];

  return (
    <div className="card h-full flex flex-col">
      {/* Header */}
      <div className="card-header">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              Property Details
            </h3>
            <div className="flex items-center text-sm text-gray-500">
              <MapPinIcon className="h-4 w-4 mr-1" />
              {property.address}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* Property Image */}
        <div className="px-6 pb-4">
          <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
            {property.images && property.images.length > 0 ? (
              <Image
                src={property.images[0]}
                alt={property.address}
                width={400}
                height={225}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <HomeIcon className="h-12 w-12 text-gray-400" />
              </div>
            )}
          </div>
        </div>

        {/* Price and Actions */}
        <div className="px-6 pb-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {formatPrice(property.price)}
              </div>
              {property.pricePerAcre && (
                <div className="text-sm text-gray-600">
                  {formatPrice(property.pricePerAcre)}/acre
                </div>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setIsWatched(!isWatched)}
                className="p-2 text-gray-400 hover:text-primary-600 rounded-lg hover:bg-gray-100"
                title={isWatched ? 'Remove from watch list' : 'Add to watch list'}
              >
                {isWatched ? (
                  <BookmarkSolidIcon className="h-5 w-5 text-primary-600" />
                ) : (
                  <BookmarkIcon className="h-5 w-5" />
                )}
              </button>
              <button
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                title="Share property"
              >
                <ShareIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {property.lotSize}
              </div>
              <div className="text-xs text-gray-500">Acres</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {property.zoning}
              </div>
              <div className="text-xs text-gray-500">Zoning</div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="px-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="flex-1 px-6 py-4 overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="space-y-4">
              {/* Features */}
              {property.features && property.features.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Features</h4>
                  <div className="flex flex-wrap gap-2">
                    {property.features.map((feature: string) => (
                      <span key={feature} className="badge-info">
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Basic Info */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Property Info</h4>
                <div className="space-y-2 text-sm">
                  {property.mlsNumber && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">MLS #:</span>
                      <span className="font-medium">{property.mlsNumber}</span>
                    </div>
                  )}
                  {property.propertyType && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Type:</span>
                      <span className="font-medium">{property.propertyType}</span>
                    </div>
                  )}
                  {property.daysOnMarket && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Days on Market:</span>
                      <span className="font-medium">{property.daysOnMarket}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'details' && (
            <div className="space-y-4">
              {/* Utilities */}
              {property.utilities && property.utilities.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Utilities</h4>
                  <div className="flex flex-wrap gap-2">
                    {property.utilities.map((utility: string) => (
                      <span key={utility} className="badge-success">
                        {utility}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Property Details */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Details</h4>
                <div className="space-y-2 text-sm">
                  {property.roadAccess && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Road Access:</span>
                      <span className="font-medium">{property.roadAccess}</span>
                    </div>
                  )}
                  {property.topography && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Topography:</span>
                      <span className="font-medium">{property.topography}</span>
                    </div>
                  )}
                  {property.soilType && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Soil Type:</span>
                      <span className="font-medium">{property.soilType}</span>
                    </div>
                  )}
                  {property.floodZone && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Flood Zone:</span>
                      <span className="font-medium">{property.floodZone}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'analysis' && (
            <div className="space-y-4">
              {/* Development Potential */}
              {property.developmentPotential && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Development Potential</h4>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-blue-800">Score</span>
                      <span className="text-lg font-bold text-blue-900">
                        {property.developmentPotential.score}/100
                      </span>
                    </div>
                    <div className="w-full bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${property.developmentPotential.score}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Market Analysis */}
              {property.marketAnalysis && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Market Analysis</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Market Trend:</span>
                      <span className={`font-medium capitalize ${
                        property.marketAnalysis.marketTrend === 'up' ? 'text-green-600' :
                        property.marketAnalysis.marketTrend === 'down' ? 'text-red-600' :
                        'text-gray-600'
                      }`}>
                        {property.marketAnalysis.marketTrend}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Demand Level:</span>
                      <span className="font-medium capitalize">
                        {property.marketAnalysis.demandLevel}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Investment Score:</span>
                      <span className="font-medium">
                        {property.marketAnalysis.investmentPotential}/100
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex space-x-3">
            <button className="btn-primary flex-1">
              <ChartBarIcon className="h-4 w-4 mr-2" />
              Analyze
            </button>
            <button className="btn-secondary flex-1">
              View Full Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
