'use client';

import { useState } from 'react';
import {
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

interface MapFiltersProps {
  filters: {
    priceRange: [number, number];
    lotSizeRange: [number, number];
    zoning: string[];
    features: string[];
    location: string;
  };
  onFiltersChange: (filters: any) => void;
  propertyCount: number;
  isLoading: boolean;
}

const zoningOptions = [
  'Residential',
  'Commercial',
  'Agricultural',
  'Industrial',
  'Mixed Use',
];

const featureOptions = [
  'Waterfront',
  'Highway Access',
  'Utilities Available',
  'Wooded',
  'Cleared',
  'Corner Lot',
  'Cul-de-sac',
  'Private Road',
];

export function MapFilters({ filters, onFiltersChange, propertyCount, isLoading }: MapFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handlePriceRangeChange = (index: number, value: string) => {
    const newRange = [...localFilters.priceRange] as [number, number];
    newRange[index] = parseInt(value) || 0;
    handleFilterChange('priceRange', newRange);
  };

  const handleLotSizeRangeChange = (index: number, value: string) => {
    const newRange = [...localFilters.lotSizeRange] as [number, number];
    newRange[index] = parseFloat(value) || 0;
    handleFilterChange('lotSizeRange', newRange);
  };

  const handleZoningChange = (zoning: string) => {
    const newZoning = localFilters.zoning.includes(zoning)
      ? localFilters.zoning.filter(z => z !== zoning)
      : [...localFilters.zoning, zoning];
    handleFilterChange('zoning', newZoning);
  };

  const handleFeatureChange = (feature: string) => {
    const newFeatures = localFilters.features.includes(feature)
      ? localFilters.features.filter(f => f !== feature)
      : [...localFilters.features, feature];
    handleFilterChange('features', newFeatures);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      priceRange: [0, 1000000] as [number, number],
      lotSizeRange: [0, 50] as [number, number],
      zoning: [],
      features: [],
      location: '',
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = 
    localFilters.priceRange[0] > 0 || 
    localFilters.priceRange[1] < 1000000 ||
    localFilters.lotSizeRange[0] > 0 ||
    localFilters.lotSizeRange[1] < 50 ||
    localFilters.zoning.length > 0 ||
    localFilters.features.length > 0 ||
    localFilters.location.length > 0;

  return (
    <div className="card h-fit">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <AdjustmentsHorizontalIcon className="h-5 w-5 text-primary-500 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
          </div>
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear all
            </button>
          )}
        </div>
        <div className="mt-2 flex items-center justify-between">
          <p className="text-sm text-gray-500">
            {isLoading ? 'Loading...' : `${propertyCount} properties found`}
          </p>
        </div>
      </div>
      
      <div className="card-body space-y-6">
        {/* Location Search */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              className="input-field pl-10"
              placeholder="City, county, or address..."
              value={localFilters.location}
              onChange={(e) => handleFilterChange('location', e.target.value)}
            />
          </div>
        </div>

        {/* Price Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Price Range
          </label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <input
                type="number"
                className="input-field"
                placeholder="Min price"
                value={localFilters.priceRange[0] || ''}
                onChange={(e) => handlePriceRangeChange(0, e.target.value)}
              />
            </div>
            <div>
              <input
                type="number"
                className="input-field"
                placeholder="Max price"
                value={localFilters.priceRange[1] === 1000000 ? '' : localFilters.priceRange[1]}
                onChange={(e) => handlePriceRangeChange(1, e.target.value || '1000000')}
              />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            ${localFilters.priceRange[0].toLocaleString()} - ${localFilters.priceRange[1].toLocaleString()}
          </div>
        </div>

        {/* Lot Size Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Lot Size (acres)
          </label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <input
                type="number"
                step="0.1"
                className="input-field"
                placeholder="Min acres"
                value={localFilters.lotSizeRange[0] || ''}
                onChange={(e) => handleLotSizeRangeChange(0, e.target.value)}
              />
            </div>
            <div>
              <input
                type="number"
                step="0.1"
                className="input-field"
                placeholder="Max acres"
                value={localFilters.lotSizeRange[1] === 50 ? '' : localFilters.lotSizeRange[1]}
                onChange={(e) => handleLotSizeRangeChange(1, e.target.value || '50')}
              />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {localFilters.lotSizeRange[0]} - {localFilters.lotSizeRange[1]} acres
          </div>
        </div>

        {/* Zoning */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Zoning
          </label>
          <div className="space-y-2">
            {zoningOptions.map((zoning) => (
              <label key={zoning} className="flex items-center">
                <input
                  type="checkbox"
                  className="checkbox mr-2"
                  checked={localFilters.zoning.includes(zoning)}
                  onChange={() => handleZoningChange(zoning)}
                />
                <span className="text-sm text-gray-700">{zoning}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Features */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Features
          </label>
          <div className="space-y-2">
            {featureOptions.map((feature) => (
              <label key={feature} className="flex items-center">
                <input
                  type="checkbox"
                  className="checkbox mr-2"
                  checked={localFilters.features.includes(feature)}
                  onChange={() => handleFeatureChange(feature)}
                />
                <span className="text-sm text-gray-700">{feature}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Active Filters</h4>
            <div className="flex flex-wrap gap-2">
              {localFilters.zoning.map((zoning) => (
                <span
                  key={zoning}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary-100 text-primary-800"
                >
                  {zoning}
                  <button
                    onClick={() => handleZoningChange(zoning)}
                    className="ml-1 hover:text-primary-600"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              ))}
              {localFilters.features.map((feature) => (
                <span
                  key={feature}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800"
                >
                  {feature}
                  <button
                    onClick={() => handleFeatureChange(feature)}
                    className="ml-1 hover:text-green-600"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
