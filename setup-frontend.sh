#!/bin/bash

# =============================================================================
# PropBolt Brain Frontend Setup Script
# =============================================================================
# This script sets up the Next.js frontend for PropBolt Brain with all
# dependencies, configurations, and development environment.
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 PropBolt Brain Frontend Setup${NC}"
echo "=================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed. Please install Node.js 18+ first.${NC}"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo -e "${RED}❌ Node.js version 18+ is required. Current version: $(node -v)${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js version: $(node -v)${NC}"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm is not installed.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ npm version: $(npm -v)${NC}"

# Install dependencies
echo -e "\n${YELLOW}📦 Installing dependencies...${NC}"
npm install

# Install additional development dependencies
echo -e "\n${YELLOW}🔧 Installing development dependencies...${NC}"
npm install --save-dev @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio

# Create necessary directories
echo -e "\n${YELLOW}📁 Creating directory structure...${NC}"
mkdir -p src/components/ui
mkdir -p src/components/layout
mkdir -p src/components/dashboard
mkdir -p src/components/map
mkdir -p src/lib/api
mkdir -p src/hooks
mkdir -p src/types
mkdir -p src/utils
mkdir -p public/images

# Copy logo file if it exists
if [ -f "public/logo-one.svg" ]; then
    echo -e "${GREEN}✅ Logo file found: public/logo-one.svg${NC}"
else
    echo -e "${YELLOW}⚠️  Logo file not found. Please ensure public/logo-one.svg exists.${NC}"
fi

# Set up environment variables
echo -e "\n${YELLOW}🔐 Setting up environment variables...${NC}"
if [ ! -f ".env.local" ]; then
    echo -e "${YELLOW}Creating .env.local file...${NC}"
    # The .env.local file is already created by the previous command
    echo -e "${GREEN}✅ Environment file created${NC}"
else
    echo -e "${GREEN}✅ Environment file already exists${NC}"
fi

# Display environment setup instructions
echo -e "\n${BLUE}🔧 Environment Configuration Required:${NC}"
echo "Please update the following in your .env.local file:"
echo ""
echo "1. NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here"
echo "   Get your token from: https://account.mapbox.com/access-tokens/"
echo ""
echo "2. NEXT_PUBLIC_API_BASE_URL=your_backend_api_url"
echo "   This should point to your Go backend API"
echo ""
echo "3. NEXT_PUBLIC_REAL_ESTATE_API_KEY=your_api_key"
echo "   Your RealEstateAPI.com API key"
echo ""

# Build the project to check for errors
echo -e "\n${YELLOW}🏗️  Building project to check for errors...${NC}"
if npm run build; then
    echo -e "${GREEN}✅ Build successful!${NC}"
else
    echo -e "${RED}❌ Build failed. Please check the errors above.${NC}"
    exit 1
fi

# Create a simple README for the frontend
cat > README-FRONTEND.md << 'EOF'
# PropBolt Brain Frontend

## Quick Start

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables:
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your API keys
   ```

3. Start development server:
   ```bash
   npm run dev
   ```

4. Open http://localhost:3000

## Environment Variables

- `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN` - Mapbox API token for maps
- `NEXT_PUBLIC_API_BASE_URL` - Backend API URL
- `NEXT_PUBLIC_REAL_ESTATE_API_KEY` - RealEstateAPI.com key

## Features

- 🗺️ Interactive property maps with GeoJSON support
- 📊 Real-time dashboard with analytics
- 🔍 Advanced property search and filtering
- 📱 Responsive design for all devices
- 🎨 Modern UI with Tailwind CSS
- ⚡ Fast performance with Next.js 14

## Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

## Architecture

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS
- **Maps**: Mapbox GL JS with react-map-gl
- **State Management**: React Query (TanStack Query)
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts
- **Icons**: Heroicons
EOF

# Success message
echo -e "\n${GREEN}🎉 Frontend setup completed successfully!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Update your .env.local file with the required API keys"
echo "2. Start the development server: npm run dev"
echo "3. Open http://localhost:3000 in your browser"
echo ""
echo -e "${YELLOW}📚 Documentation:${NC}"
echo "- Frontend README: README-FRONTEND.md"
echo "- Component documentation in src/components/"
echo "- API integration in src/lib/api/"
echo ""
echo -e "${GREEN}✨ PropBolt Brain Frontend is ready for development!${NC}"
