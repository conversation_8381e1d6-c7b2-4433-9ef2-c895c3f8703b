{"name": "propbolt-brain-frontend", "version": "1.0.0", "description": "PropBolt Brain - Admin Vacant Land Analysis Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "mapbox-gl": "^2.15.0", "react-map-gl": "^7.1.0", "@types/mapbox-gl": "^2.7.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "framer-motion": "^10.16.0", "react-hot-toast": "^2.4.0", "@tanstack/react-query": "^4.36.0", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "lucide-react": "^0.292.0", "react-select": "^5.8.0", "react-virtualized-auto-sizer": "^1.0.0", "react-window": "^1.8.0", "@types/react-window": "^1.8.0", "react-intersection-observer": "^9.5.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "engines": {"node": ">=18.0.0"}}