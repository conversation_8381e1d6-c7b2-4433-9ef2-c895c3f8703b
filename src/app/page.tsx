'use client';

import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { DashboardHeader } from '@/components/dashboard/DashboardHeader';
import { StatsOverview } from '@/components/dashboard/StatsOverview';
import { ActiveSearches } from '@/components/dashboard/ActiveSearches';
import { LatestListings } from '@/components/dashboard/LatestListings';
import { HighEquityDeals } from '@/components/dashboard/HighEquityDeals';
import { WatchListPreview } from '@/components/dashboard/WatchListPreview';
import { MarketPulse } from '@/components/dashboard/MarketPulse';
import { QuickActions } from '@/components/dashboard/QuickActions';

export default function DashboardPage() {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Dashboard Header */}
        <DashboardHeader currentTime={currentTime} />

        {/* Stats Overview */}
        <StatsOverview />

        {/* Quick Actions */}
        <QuickActions />

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - 2/3 width */}
          <div className="lg:col-span-2 space-y-6">
            {/* Active Searches */}
            <ActiveSearches />

            {/* Latest Listings */}
            <LatestListings />

            {/* High Equity Deals */}
            <HighEquityDeals />
          </div>

          {/* Right Column - 1/3 width */}
          <div className="space-y-6">
            {/* Watch List Preview */}
            <WatchListPreview />

            {/* Market Pulse */}
            <MarketPulse />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
