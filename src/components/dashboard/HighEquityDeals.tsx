'use client';

import Link from 'next/link';
import {
  ArrowTrendingUpIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  FireIcon,
} from '@heroicons/react/24/outline';

// Mock data for high equity deals
const highEquityDeals = [
  {
    id: 1,
    address: '2468 Riverside Dr, Ormond Beach, FL',
    currentPrice: 65000,
    estimatedValue: 95000,
    equityPotential: 30000,
    equityPercentage: 46,
    lotSize: 3.2,
    zoning: 'Residential',
    reason: 'Below market pricing, waterfront access',
    daysOnMarket: 45,
    confidence: 'High',
  },
  {
    id: 2,
    address: '1357 State Road 44, New Smyrna Beach, FL',
    currentPrice: 85000,
    estimatedValue: 115000,
    equityPotential: 30000,
    equityPercentage: 35,
    lotSize: 5.8,
    zoning: 'Commercial',
    reason: 'Highway frontage, development potential',
    daysOnMarket: 62,
    confidence: 'Medium',
  },
  {
    id: 3,
    address: '9876 Orange Ave, Deltona, FL',
    currentPrice: 28000,
    estimatedValue: 38000,
    equityPotential: 10000,
    equityPercentage: 36,
    lotSize: 1.8,
    zoning: 'Residential',
    reason: 'Motivated seller, quick sale needed',
    daysOnMarket: 28,
    confidence: 'High',
  },
];

export function HighEquityDeals() {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getConfidenceColor = (confidence: string) => {
    switch (confidence.toLowerCase()) {
      case 'high':
        return 'text-green-600 bg-green-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FireIcon className="h-5 w-5 text-orange-500 mr-2" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">High Equity Deals</h3>
              <p className="text-sm text-gray-500">Properties with significant equity potential</p>
            </div>
          </div>
          <Link
            href="/analyzer?filter=high-equity"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View all →
          </Link>
        </div>
      </div>
      <div className="card-body">
        <div className="space-y-4">
          {highEquityDeals.map((deal) => (
            <div
              key={deal.id}
              className="border border-gray-200 rounded-lg p-4 hover:border-orange-300 hover:shadow-md transition-all duration-200"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h4 className="text-base font-medium text-gray-900 mb-1">
                    {deal.address}
                  </h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{deal.lotSize} acres</span>
                    <span className="badge-info">{deal.zoning}</span>
                    <span className={`badge ${getConfidenceColor(deal.confidence)}`}>
                      {deal.confidence} Confidence
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">
                    +{deal.equityPercentage}%
                  </div>
                  <div className="text-xs text-gray-500">Equity Potential</div>
                </div>
              </div>

              {/* Financial Details */}
              <div className="grid grid-cols-3 gap-4 mb-3">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-500 mr-1" />
                    <span className="text-xs text-gray-500">Current Price</span>
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    {formatPrice(deal.currentPrice)}
                  </div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <ChartBarIcon className="h-4 w-4 text-blue-500 mr-1" />
                    <span className="text-xs text-blue-600">Est. Value</span>
                  </div>
                  <div className="text-lg font-semibold text-blue-700">
                    {formatPrice(deal.estimatedValue)}
                  </div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">Equity Gain</span>
                  </div>
                  <div className="text-lg font-semibold text-green-700">
                    {formatPrice(deal.equityPotential)}
                  </div>
                </div>
              </div>

              {/* Analysis Reason */}
              <div className="mb-3">
                <div className="text-xs text-gray-500 mb-1">Analysis:</div>
                <div className="text-sm text-gray-700">{deal.reason}</div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                <div className="text-xs text-gray-500">
                  {deal.daysOnMarket} days on market
                </div>
                <div className="flex space-x-2">
                  <Link
                    href={`/analyzer/${deal.id}`}
                    className="btn-secondary text-xs px-3 py-1"
                  >
                    Analyze
                  </Link>
                  <Link
                    href={`/property/${deal.id}`}
                    className="btn-primary text-xs px-3 py-1"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {highEquityDeals.length === 0 && (
          <div className="text-center py-8">
            <FireIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-sm font-medium text-gray-900 mb-2">No high equity deals found</h3>
            <p className="text-sm text-gray-500 mb-4">
              We'll notify you when properties with high equity potential become available
            </p>
            <Link href="/analyzer" className="btn-primary">
              Run Analysis
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
