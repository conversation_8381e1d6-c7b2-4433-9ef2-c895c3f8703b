#!/bin/bash

# =============================================================================
# PropBolt Brain Enterprise Deployment Verification Script
# =============================================================================
# This script verifies that all enterprise features are properly deployed
# including auto-scaling, load balancing, HA PostgreSQL, and marketplace apps.
# =============================================================================

set -e

# Configuration
PROJECT_ID="brain-propbolt-prod"
REGION="us-east1"
SERVICE_NAME="brain-api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 PropBolt Brain Enterprise Deployment Verification${NC}"
echo "=================================================="

# Verification counters
CHECKS_PASSED=0
CHECKS_FAILED=0

# Function to check a condition
check_condition() {
    local description=$1
    local command=$2
    local expected_result=$3
    
    echo -e "\n${YELLOW}Checking:${NC} $description"
    
    if eval "$command" >/dev/null 2>&1; then
        if [ -n "$expected_result" ]; then
            result=$(eval "$command" 2>/dev/null)
            if [[ "$result" == *"$expected_result"* ]]; then
                echo -e "${GREEN}✅ PASS${NC} - $description"
                ((CHECKS_PASSED++))
            else
                echo -e "${RED}❌ FAIL${NC} - $description (Expected: $expected_result, Got: $result)"
                ((CHECKS_FAILED++))
            fi
        else
            echo -e "${GREEN}✅ PASS${NC} - $description"
            ((CHECKS_PASSED++))
        fi
    else
        echo -e "${RED}❌ FAIL${NC} - $description"
        ((CHECKS_FAILED++))
    fi
}

# =============================================================================
# Verify Google Cloud CLI Usage
# =============================================================================
echo -e "\n${BLUE}=== Verifying Google Cloud CLI Configuration ===${NC}"

check_condition "Google Cloud CLI installed" "gcloud version"
check_condition "Project set correctly" "gcloud config get-value project" "$PROJECT_ID"
check_condition "Required APIs enabled" "gcloud services list --enabled --filter='name:run.googleapis.com'"

# =============================================================================
# Verify Auto-Scaling Configuration
# =============================================================================
echo -e "\n${BLUE}=== Verifying Auto-Scaling Configuration ===${NC}"

check_condition "Cloud Run service exists" "gcloud run services describe $SERVICE_NAME --region=$REGION"
check_condition "Minimum instances set to 2" "gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(spec.template.metadata.annotations.\"autoscaling.knative.dev/minScale\")'" "2"
check_condition "Maximum instances set to 100" "gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(spec.template.metadata.annotations.\"autoscaling.knative.dev/maxScale\")'" "100"
check_condition "Concurrency set to 1000" "gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(spec.template.spec.containerConcurrency)'" "1000"
check_condition "CPU boost enabled" "gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(spec.template.metadata.annotations.\"run.googleapis.com/cpu-boost\")'" "true"

# =============================================================================
# Verify Load Balancing
# =============================================================================
echo -e "\n${BLUE}=== Verifying Load Balancing Configuration ===${NC}"

check_condition "Backend service exists" "gcloud compute backend-services describe brain-backend-service --global"
check_condition "URL map exists" "gcloud compute url-maps describe brain-url-map --global"
check_condition "HTTPS proxy exists" "gcloud compute target-https-proxies describe brain-https-proxy --global"
check_condition "Forwarding rule exists" "gcloud compute forwarding-rules describe brain-forwarding-rule --global"
check_condition "CDN enabled" "gcloud compute backend-services describe brain-backend-service --global --format='value(enableCDN)'" "True"

# =============================================================================
# Verify PostgreSQL High Availability
# =============================================================================
echo -e "\n${BLUE}=== Verifying PostgreSQL High Availability ===${NC}"

check_condition "Primary database exists" "gcloud sql instances describe brain-propbolt-db"
check_condition "Regional availability" "gcloud sql instances describe brain-propbolt-db --format='value(settings.availabilityType)'" "REGIONAL"
check_condition "Backups enabled" "gcloud sql instances describe brain-propbolt-db --format='value(settings.backupConfiguration.enabled)'" "True"
check_condition "Point-in-time recovery enabled" "gcloud sql instances describe brain-propbolt-db --format='value(settings.backupConfiguration.pointInTimeRecoveryEnabled)'" "True"
check_condition "Read replica 1 exists" "gcloud sql instances describe brain-propbolt-db-replica-1"
check_condition "Read replica 2 exists" "gcloud sql instances describe brain-propbolt-db-replica-2"

# =============================================================================
# Verify Marketplace Applications
# =============================================================================
echo -e "\n${BLUE}=== Verifying Google Cloud Marketplace Applications ===${NC}"

check_condition "Deployment Manager enabled" "gcloud services list --enabled --filter='name:deploymentmanager.googleapis.com'"
check_condition "Datadog deployment exists" "gcloud deployment-manager deployments describe datadog-monitoring"
check_condition "Grafana deployment exists" "gcloud deployment-manager deployments describe grafana-enterprise"
check_condition "Redis Enterprise deployment exists" "gcloud deployment-manager deployments describe redis-enterprise"

# =============================================================================
# Verify Monitoring and Logging
# =============================================================================
echo -e "\n${BLUE}=== Verifying Monitoring and Logging ===${NC}"

check_condition "Cloud Monitoring enabled" "gcloud services list --enabled --filter='name:monitoring.googleapis.com'"
check_condition "Cloud Logging enabled" "gcloud services list --enabled --filter='name:logging.googleapis.com'"
check_condition "Cloud Trace enabled" "gcloud services list --enabled --filter='name:cloudtrace.googleapis.com'"
check_condition "Cloud Profiler enabled" "gcloud services list --enabled --filter='name:cloudprofiler.googleapis.com'"
check_condition "API error metrics exist" "gcloud logging metrics describe api_errors"
check_condition "API latency metrics exist" "gcloud logging metrics describe api_latency"
check_condition "API request metrics exist" "gcloud logging metrics describe api_requests"

# =============================================================================
# Verify Security Configuration
# =============================================================================
echo -e "\n${BLUE}=== Verifying Security Configuration ===${NC}"

check_condition "Security Command Center enabled" "gcloud services list --enabled --filter='name:securitycenter.googleapis.com'"
check_condition "Cloud Armor security policy exists" "gcloud compute security-policies describe brain-security-policy"
check_condition "VPC network exists" "gcloud compute networks describe brain-vpc"
check_condition "VPC subnet exists" "gcloud compute networks subnets describe brain-subnet --region=$REGION"
check_condition "HTTPS firewall rule exists" "gcloud compute firewall-rules describe brain-allow-https"
check_condition "Health check firewall rule exists" "gcloud compute firewall-rules describe brain-allow-health-check"
check_condition "Service account exists" "gcloud iam service-accounts describe brain-api-service@$PROJECT_ID.iam.gserviceaccount.com"

# =============================================================================
# Verify Storage and Caching
# =============================================================================
echo -e "\n${BLUE}=== Verifying Storage and Caching ===${NC}"

check_condition "Cloud Storage bucket exists" "gsutil ls gs://brain-propbolt-storage"
check_condition "Redis cache instance exists" "gcloud redis instances describe brain-propbolt-cache --region=$REGION"

# =============================================================================
# Verify API Endpoints
# =============================================================================
echo -e "\n${BLUE}=== Verifying API Endpoints ===${NC}"

# Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)" 2>/dev/null || echo "")

if [ -n "$SERVICE_URL" ]; then
    echo "Service URL: $SERVICE_URL"
    
    # Test health endpoint
    if curl -s -f "$SERVICE_URL/" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC} - Health endpoint accessible"
        ((CHECKS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC} - Health endpoint not accessible"
        ((CHECKS_FAILED++))
    fi
    
    # Test CORS headers
    cors_response=$(curl -s -I -X OPTIONS -H "Origin: https://brain.propbolt.com" "$SERVICE_URL/" 2>/dev/null || echo "")
    if echo "$cors_response" | grep -q "Access-Control-Allow-Origin"; then
        echo -e "${GREEN}✅ PASS${NC} - CORS headers configured"
        ((CHECKS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC} - CORS headers missing"
        ((CHECKS_FAILED++))
    fi
else
    echo -e "${RED}❌ FAIL${NC} - Could not retrieve service URL"
    ((CHECKS_FAILED++))
fi

# =============================================================================
# Verification Summary
# =============================================================================
echo -e "\n${BLUE}=== Enterprise Deployment Verification Summary ===${NC}"
echo "=================================================="
echo -e "Checks Passed: ${GREEN}$CHECKS_PASSED${NC}"
echo -e "Checks Failed: ${RED}$CHECKS_FAILED${NC}"
echo "Total Checks: $((CHECKS_PASSED + CHECKS_FAILED))"

if [ $CHECKS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL ENTERPRISE FEATURES VERIFIED SUCCESSFULLY!${NC}"
    echo ""
    echo "✅ Google Cloud CLI deployment confirmed"
    echo "✅ Auto-scaling (2-100 instances) configured"
    echo "✅ Global load balancing with CDN enabled"
    echo "✅ High-availability PostgreSQL with read replicas"
    echo "✅ Google Cloud Marketplace applications installed"
    echo "✅ Enterprise monitoring and security configured"
    echo ""
    echo "🚀 PropBolt Brain Enterprise is production-ready!"
    exit 0
else
    echo -e "\n${RED}⚠️  Some enterprise features need attention.${NC}"
    echo ""
    echo "Common issues and solutions:"
    echo "1. Ensure all required APIs are enabled"
    echo "2. Check IAM permissions for service accounts"
    echo "3. Verify marketplace application configurations"
    echo "4. Confirm network and firewall settings"
    echo "5. Review deployment logs for detailed errors"
    echo ""
    echo "Run the deployment script again if needed:"
    echo "sudo ./deploy.sh"
    exit 1
fi
