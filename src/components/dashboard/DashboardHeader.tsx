'use client';

import { format } from 'date-fns';

interface DashboardHeaderProps {
  currentTime: Date;
}

export function DashboardHeader({ currentTime }: DashboardHeaderProps) {
  // Mock user data - replace with actual auth context
  const userName = 'Admin';
  
  const formattedDate = format(currentTime, 'EEEE, MMMM do, yyyy');
  const formattedTime = format(currentTime, 'h:mm a');

  return (
    <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-lg">
      <div className="px-6 py-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              Hello, {userName}! 👋
            </h1>
            <p className="text-primary-100 mt-2 text-lg">
              Today is {formattedDate}
            </p>
            <p className="text-primary-200 text-sm">
              Current time: {formattedTime}
            </p>
          </div>
          
          {/* Weather widget placeholder */}
          <div className="hidden md:block">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-white">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">☀️</div>
                <div>
                  <div className="text-lg font-semibold">72°F</div>
                  <div className="text-sm text-primary-100">Daytona Beach, FL</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Quick stats bar */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
            <div className="text-primary-100 text-sm">Active Searches</div>
            <div className="text-white text-xl font-bold">12</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
            <div className="text-primary-100 text-sm">Watch List</div>
            <div className="text-white text-xl font-bold">47</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
            <div className="text-primary-100 text-sm">New Listings</div>
            <div className="text-white text-xl font-bold">23</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
            <div className="text-primary-100 text-sm">Alerts</div>
            <div className="text-white text-xl font-bold">8</div>
          </div>
        </div>
      </div>
    </div>
  );
}
