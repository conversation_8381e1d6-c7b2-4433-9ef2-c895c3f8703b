#!/bin/bash

# =============================================================================
# PropBolt Brain API Test Script
# =============================================================================
# This script tests the PropBolt Brain API endpoints to ensure they're working
# correctly in both local development and production environments.
# =============================================================================

set -e

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8080}"
REAL_ESTATE_API_KEY="${REAL_ESTATE_API_KEY:-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914}"

echo "🧪 Testing PropBolt Brain API at: $API_BASE_URL"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    local expected_status=${5:-200}
    
    echo -e "\n${YELLOW}Testing:${NC} $description"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$API_BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE_URL$endpoint")
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} - Status: $status_code"
        ((TESTS_PASSED++))
        
        # Pretty print JSON if it's valid
        if echo "$body" | jq . >/dev/null 2>&1; then
            echo "Response preview:"
            echo "$body" | jq . | head -10
            if [ $(echo "$body" | jq . | wc -l) -gt 10 ]; then
                echo "... (truncated)"
            fi
        else
            echo "Response: $body"
        fi
    else
        echo -e "${RED}❌ FAIL${NC} - Expected: $expected_status, Got: $status_code"
        echo "Response: $body"
        ((TESTS_FAILED++))
    fi
}

# =============================================================================
# Test Legacy PropBolt Endpoints
# =============================================================================
echo -e "\n${YELLOW}=== Testing Legacy PropBolt Endpoints ===${NC}"

# Health Check
test_endpoint "GET" "/" "Health Check"

# Property Details (this might fail without proper setup, but we test the endpoint)
test_endpoint "GET" "/property?address=123 Main St, Daytona Beach, FL" "Property Details by Address" "" "200"

# Enhanced Search (this might fail without proper geographic bounds)
test_endpoint "GET" "/search/for-sale-enhanced?neLat=29.2200&neLong=-81.0100&swLat=29.2000&swLong=-81.0300&lotSizeMin=5000" "Enhanced Land Search" "" "200"

# Autocomplete
test_endpoint "GET" "/autocomplete?q=123 Main" "Autocomplete" "" "200"

# =============================================================================
# Test RealEstateAPI.com Proxy Endpoints
# =============================================================================
echo -e "\n${YELLOW}=== Testing RealEstateAPI.com Proxy Endpoints ===${NC}"

# AutoComplete
test_endpoint "POST" "/api/v1/proxy/autocomplete" "AutoComplete API" \
    '{"input": "123 Main St, Daytona"}' "200"

# Property Mapping
test_endpoint "POST" "/api/v1/proxy/mapping" "Property Mapping API" \
    '{"query": {"location": {"city": "Daytona Beach", "state": "FL"}}}' "200"

# Property Detail
test_endpoint "POST" "/api/v1/proxy/property-detail" "Property Detail API" \
    '{"address": "123 Main St, Daytona Beach, FL", "comps": false}' "200"

# Property Search
test_endpoint "POST" "/api/v1/proxy/property-search" "Property Search API" \
    '{"query": {"location": {"city": "Daytona Beach", "state": "FL"}}, "limit": 10}' "200"

# Involuntary Liens
test_endpoint "POST" "/api/v1/proxy/involuntary-liens" "Involuntary Liens API" \
    '{"address": "123 Main St, Daytona Beach, FL"}' "200"

# Property Comps v3
test_endpoint "POST" "/api/v1/proxy/property-comps-v3" "Property Comps v3 API" \
    '{"address": "123 Main St, Daytona Beach, FL", "maxComps": 5}' "200"

# Property Comps v2
test_endpoint "POST" "/api/v1/proxy/property-comps-v2" "Property Comps v2 API" \
    '{"address": "123 Main St, Daytona Beach, FL", "maxComps": 5}' "200"

# SkipTrace
test_endpoint "POST" "/api/v1/proxy/skiptrace" "SkipTrace API" \
    '{"address": "123 Main St", "city": "Daytona Beach", "state": "FL"}' "200"

# =============================================================================
# Test Error Handling
# =============================================================================
echo -e "\n${YELLOW}=== Testing Error Handling ===${NC}"

# Test invalid endpoint
test_endpoint "GET" "/invalid-endpoint" "Invalid Endpoint" "" "404"

# Test missing required parameters
test_endpoint "POST" "/api/v1/proxy/autocomplete" "Missing Input Parameter" \
    '{}' "400"

# Test invalid JSON
test_endpoint "POST" "/api/v1/proxy/autocomplete" "Invalid JSON" \
    'invalid json' "400"

# =============================================================================
# Test CORS Headers
# =============================================================================
echo -e "\n${YELLOW}=== Testing CORS Headers ===${NC}"

echo "Testing CORS preflight request..."
cors_response=$(curl -s -I -X OPTIONS \
    -H "Origin: https://brain.propbolt.com" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: Content-Type" \
    "$API_BASE_URL/api/v1/proxy/autocomplete")

if echo "$cors_response" | grep -q "Access-Control-Allow-Origin"; then
    echo -e "${GREEN}✅ PASS${NC} - CORS headers present"
    ((TESTS_PASSED++))
else
    echo -e "${RED}❌ FAIL${NC} - CORS headers missing"
    ((TESTS_FAILED++))
fi

# =============================================================================
# Test Results Summary
# =============================================================================
echo -e "\n${YELLOW}=== Test Results Summary ===${NC}"
echo "=================================================="
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! PropBolt Brain API is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed. Please check the API configuration and try again.${NC}"
    echo ""
    echo "Common issues:"
    echo "1. Make sure the API server is running"
    echo "2. Check that REAL_ESTATE_API_KEY is set correctly"
    echo "3. Verify network connectivity to external APIs"
    echo "4. Check server logs for detailed error information"
    exit 1
fi
