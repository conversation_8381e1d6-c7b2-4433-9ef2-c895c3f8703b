'use client';

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import {
  TrendingUpIcon,
  TrendingDownIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';

// Mock market data
const marketData = [
  { month: 'Jul', avgPrice: 8200, listings: 145 },
  { month: 'Aug', avgPrice: 8450, listings: 132 },
  { month: 'Sep', avgPrice: 8300, listings: 156 },
  { month: 'Oct', avgPrice: 8650, listings: 143 },
  { month: 'Nov', avgPrice: 8800, listings: 128 },
  { month: 'Dec', avgPrice: 8950, listings: 134 },
  { month: 'Jan', avgPrice: 9100, listings: 142 },
];

const marketMetrics = [
  {
    name: 'Avg Price/Acre',
    value: '$9,100',
    change: '+7.2%',
    changeType: 'increase',
    period: 'vs last month',
  },
  {
    name: 'Active Listings',
    value: '142',
    change: '+6.0%',
    changeType: 'increase',
    period: 'vs last month',
  },
  {
    name: 'Days on Market',
    value: '45',
    change: '-12%',
    changeType: 'decrease',
    period: 'avg this month',
  },
  {
    name: 'Market Activity',
    value: '89%',
    change: '-2.1%',
    changeType: 'decrease',
    period: 'vs last month',
  },
];

export function MarketPulse() {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center">
          <ChartBarIcon className="h-5 w-5 text-primary-500 mr-2" />
          <div>
            <h3 className="text-lg font-medium text-gray-900">Market Pulse</h3>
            <p className="text-sm text-gray-500">Volusia County vacant land trends</p>
          </div>
        </div>
      </div>
      <div className="card-body">
        {/* Price Trend Chart */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Average Price per Acre</h4>
          <div className="h-32">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={marketData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="month" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                />
                <YAxis 
                  hide
                  domain={['dataMin - 200', 'dataMax + 200']}
                />
                <Tooltip
                  formatter={(value: number) => [formatPrice(value), 'Avg Price/Acre']}
                  labelStyle={{ color: '#374151' }}
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="avgPrice"
                  stroke="#0ea5e9"
                  strokeWidth={2}
                  dot={{ fill: '#0ea5e9', strokeWidth: 2, r: 3 }}
                  activeDot={{ r: 5, stroke: '#0ea5e9', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Market Metrics */}
        <div className="space-y-4">
          {marketMetrics.map((metric) => (
            <div key={metric.name} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">{metric.name}</div>
                <div className="text-xs text-gray-500">{metric.period}</div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-gray-900">{metric.value}</div>
                <div className="flex items-center justify-end">
                  {metric.changeType === 'increase' ? (
                    <TrendingUpIcon className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span
                    className={`text-xs font-medium ${
                      metric.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {metric.change}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Market Insights */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Market Insights</h4>
          <ul className="text-xs text-blue-800 space-y-1">
            <li>• Prices trending upward with 7.2% monthly growth</li>
            <li>• Inventory levels stable with slight increase in listings</li>
            <li>• Properties selling faster with reduced days on market</li>
            <li>• Strong demand in waterfront and commercial zones</li>
          </ul>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 flex space-x-2">
          <button className="btn-secondary text-xs px-3 py-1 flex-1">
            View Report
          </button>
          <button className="btn-primary text-xs px-3 py-1 flex-1">
            Set Alerts
          </button>
        </div>
      </div>
    </div>
  );
}
