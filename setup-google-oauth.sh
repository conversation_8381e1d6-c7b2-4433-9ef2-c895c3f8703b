#!/bin/bash

# =============================================================================
# Google OAuth Setup Script for PropBolt Brain
# =============================================================================
# This script sets up Google OAuth credentials using Google Cloud CLI
# =============================================================================

set -e

# Configuration
PROJECT_ID="brain-propbolt-prod"
OAUTH_CLIENT_NAME="propbolt-brain-oauth"
REDIRECT_URIS="https://brain.propbolt.com/api/auth/callback/google,http://localhost:3000/api/auth/callback/google"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 Setting up Google OAuth for PropBolt Brain${NC}"
echo "=================================================="

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Google Cloud CLI not found. Please install gcloud first.${NC}"
    echo "Visit: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Set the project
echo -e "${YELLOW}📋 Setting Google Cloud project...${NC}"
sudo gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required APIs...${NC}"
sudo gcloud services enable iamcredentials.googleapis.com
sudo gcloud services enable oauth2.googleapis.com
sudo gcloud services enable cloudresourcemanager.googleapis.com

# Create OAuth consent screen (if not exists)
echo -e "${YELLOW}🖥️  Setting up OAuth consent screen...${NC}"
cat > oauth-consent-config.json << EOF
{
  "applicationTitle": "PropBolt Brain",
  "applicationHomePageUrl": "https://brain.propbolt.com",
  "applicationPrivacyPolicyUrl": "https://propbolt.com/privacy",
  "applicationTermsOfServiceUrl": "https://propbolt.com/terms",
  "authorizedDomains": [
    "propbolt.com",
    "brain.propbolt.com"
  ]
}
EOF

# Create OAuth 2.0 client credentials
echo -e "${YELLOW}🔑 Creating OAuth 2.0 client credentials...${NC}"

# Check if OAuth client already exists
if sudo gcloud auth application-default print-access-token > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Google Cloud authentication verified${NC}"
else
    echo -e "${YELLOW}⚠️  Please authenticate with Google Cloud first:${NC}"
    echo "sudo gcloud auth application-default login"
    exit 1
fi

# Create OAuth client using gcloud
echo -e "${YELLOW}🔐 Creating OAuth 2.0 client...${NC}"
OAUTH_OUTPUT=$(sudo gcloud auth application-default print-access-token | head -1)

# Use Google Cloud Console API to create OAuth client
echo -e "${YELLOW}📝 Creating OAuth client configuration...${NC}"

# Create the OAuth client using REST API
cat > create-oauth-client.sh << 'EOF'
#!/bin/bash

PROJECT_ID="brain-propbolt-prod"
ACCESS_TOKEN=$(sudo gcloud auth application-default print-access-token)

# Create OAuth client
curl -X POST \
  "https://oauth2.googleapis.com/v1/projects/${PROJECT_ID}/oauthClients" \
  -H "Authorization: Bearer ${ACCESS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "displayName": "PropBolt Brain OAuth Client",
    "redirectUris": [
      "https://brain.propbolt.com/api/auth/callback/google",
      "http://localhost:3000/api/auth/callback/google"
    ],
    "grantTypes": ["authorization_code", "refresh_token"],
    "responseTypes": ["code"],
    "scopes": [
      "openid",
      "email",
      "profile"
    ]
  }'
EOF

chmod +x create-oauth-client.sh

echo -e "${BLUE}📋 Manual OAuth Setup Instructions:${NC}"
echo "=================================================="
echo ""
echo "Since OAuth client creation requires manual setup in Google Cloud Console,"
echo "please follow these steps:"
echo ""
echo "1. Go to Google Cloud Console:"
echo "   https://console.cloud.google.com/apis/credentials?project=${PROJECT_ID}"
echo ""
echo "2. Click 'Create Credentials' → 'OAuth 2.0 Client IDs'"
echo ""
echo "3. Configure the OAuth client:"
echo "   - Application type: Web application"
echo "   - Name: PropBolt Brain OAuth Client"
echo "   - Authorized redirect URIs:"
echo "     • https://brain.propbolt.com/api/auth/callback/google"
echo "     • http://localhost:3000/api/auth/callback/google"
echo ""
echo "4. Copy the Client ID and Client Secret"
echo ""
echo "5. Update your .env.local file with:"
echo "   GOOGLE_CLIENT_ID=your_client_id_here"
echo "   GOOGLE_CLIENT_SECRET=your_client_secret_here"
echo ""

# Create a helper script to update environment variables
cat > update-oauth-env.sh << 'EOF'
#!/bin/bash

echo "🔑 OAuth Environment Variable Updater"
echo "====================================="
echo ""
read -p "Enter your Google OAuth Client ID: " CLIENT_ID
read -p "Enter your Google OAuth Client Secret: " CLIENT_SECRET

# Update .env.local file
if [ -f ".env.local" ]; then
    # Check if variables already exist
    if grep -q "GOOGLE_CLIENT_ID=" .env.local; then
        sed -i.bak "s/GOOGLE_CLIENT_ID=.*/GOOGLE_CLIENT_ID=${CLIENT_ID}/" .env.local
    else
        echo "GOOGLE_CLIENT_ID=${CLIENT_ID}" >> .env.local
    fi
    
    if grep -q "GOOGLE_CLIENT_SECRET=" .env.local; then
        sed -i.bak "s/GOOGLE_CLIENT_SECRET=.*/GOOGLE_CLIENT_SECRET=${CLIENT_SECRET}/" .env.local
    else
        echo "GOOGLE_CLIENT_SECRET=${CLIENT_SECRET}" >> .env.local
    fi
    
    echo "✅ Environment variables updated in .env.local"
else
    echo "❌ .env.local file not found"
    exit 1
fi
EOF

chmod +x update-oauth-env.sh

echo -e "${GREEN}✅ OAuth setup scripts created!${NC}"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Follow the manual setup instructions above"
echo "2. Run: ./update-oauth-env.sh (to update environment variables)"
echo "3. Test OAuth login in your application"
echo ""
echo -e "${BLUE}🔧 Additional OAuth Configuration:${NC}"
echo "- OAuth consent screen: https://console.cloud.google.com/apis/credentials/consent?project=${PROJECT_ID}"
echo "- API credentials: https://console.cloud.google.com/apis/credentials?project=${PROJECT_ID}"
echo ""
echo -e "${GREEN}🎉 Google OAuth setup preparation complete!${NC}"
