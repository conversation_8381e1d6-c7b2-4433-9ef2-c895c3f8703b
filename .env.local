# PropBolt Brain Frontend Environment Configuration
# =================================================

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://brain-api-service-url.run.app
NEXT_PUBLIC_REAL_ESTATE_API_KEY=your_real_estate_api_key_here

# Mapbox Configuration (Required for map functionality)
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_access_token_here

# Authentication Configuration (BetterAuth)
BETTER_AUTH_SECRET=your_better_auth_secret_here
BETTER_AUTH_URL=https://brain.propbolt.com
NEXT_PUBLIC_BETTER_AUTH_URL=https://brain.propbolt.com

# Database Configuration (for BetterAuth)
DATABASE_URL=******************************************************/brain_propbolt_db

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=brain-propbolt-prod
GOOGLE_CLOUD_REGION=us-east1

# External API Keys
ZILLOW_API_KEY=your_zillow_api_key_here
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_EXPORT=true

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG=false
