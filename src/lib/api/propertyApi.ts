import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

interface PropertyFilters {
  priceRange: [number, number];
  lotSizeRange: [number, number];
  zoning: string[];
  features: string[];
  location: string;
}

interface Property {
  id: string;
  address: string;
  price: number;
  lotSize: number;
  coordinates: [number, number];
  zoning: string;
  features: string[];
  images?: string[];
  description?: string;
  pricePerAcre?: number;
  taxInfo?: any;
  ownerInfo?: any;
  listingDate?: string;
  daysOnMarket?: number;
  mlsNumber?: string;
  propertyType?: string;
  utilities?: string[];
  roadAccess?: string;
  topography?: string;
  soilType?: string;
  floodZone?: string;
  environmentalFactors?: string[];
  nearbyAmenities?: string[];
  developmentPotential?: {
    score: number;
    factors: string[];
    restrictions: string[];
  };
  marketAnalysis?: {
    pricePerAcreComparison: number;
    marketTrend: 'up' | 'down' | 'stable';
    demandLevel: 'high' | 'medium' | 'low';
    investmentPotential: number;
  };
}

// Mock data generator for development
const generateMockProperties = (filters: PropertyFilters): Property[] => {
  const mockProperties: Property[] = [];
  
  // Daytona Beach area coordinates
  const baseCoordinates = {
    lat: 29.2108,
    lng: -81.0228,
  };
  
  for (let i = 1; i <= 50; i++) {
    // Generate random coordinates around Daytona Beach
    const lat = baseCoordinates.lat + (Math.random() - 0.5) * 0.2;
    const lng = baseCoordinates.lng + (Math.random() - 0.5) * 0.2;
    
    const lotSize = Math.random() * 10 + 0.5; // 0.5 to 10.5 acres
    const pricePerAcre = Math.random() * 20000 + 5000; // $5K to $25K per acre
    const price = Math.round(lotSize * pricePerAcre);
    
    const zoningOptions = ['Residential', 'Commercial', 'Agricultural', 'Mixed Use'];
    const featureOptions = ['Waterfront', 'Highway Access', 'Utilities Available', 'Wooded', 'Cleared'];
    
    const property: Property = {
      id: `prop-${i}`,
      address: `${Math.floor(Math.random() * 9999) + 1000} ${['Oak', 'Pine', 'Maple', 'Cedar', 'Elm'][Math.floor(Math.random() * 5)]} ${['St', 'Ave', 'Rd', 'Ln', 'Dr'][Math.floor(Math.random() * 5)]}, Daytona Beach, FL`,
      price,
      lotSize: Math.round(lotSize * 100) / 100,
      coordinates: [lng, lat],
      zoning: zoningOptions[Math.floor(Math.random() * zoningOptions.length)],
      features: featureOptions.filter(() => Math.random() > 0.6),
      pricePerAcre: Math.round(pricePerAcre),
      listingDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      daysOnMarket: Math.floor(Math.random() * 90),
      mlsNumber: `MLS${Math.floor(Math.random() * 1000000)}`,
      propertyType: 'Vacant Land',
      utilities: ['Electric', 'Water', 'Sewer'].filter(() => Math.random() > 0.4),
      roadAccess: ['Paved', 'Gravel', 'Dirt'][Math.floor(Math.random() * 3)],
      topography: ['Flat', 'Rolling', 'Hilly'][Math.floor(Math.random() * 3)],
      soilType: ['Sandy', 'Clay', 'Loam'][Math.floor(Math.random() * 3)],
      floodZone: Math.random() > 0.8 ? 'AE' : 'X',
      developmentPotential: {
        score: Math.floor(Math.random() * 100),
        factors: ['Good location', 'Utilities nearby', 'Zoning favorable'].filter(() => Math.random() > 0.5),
        restrictions: ['Setback requirements', 'Environmental review'].filter(() => Math.random() > 0.7),
      },
      marketAnalysis: {
        pricePerAcreComparison: Math.random() * 0.4 - 0.2, // -20% to +20%
        marketTrend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as 'up' | 'down' | 'stable',
        demandLevel: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as 'high' | 'medium' | 'low',
        investmentPotential: Math.floor(Math.random() * 100),
      },
    };
    
    mockProperties.push(property);
  }
  
  // Apply filters
  return mockProperties.filter(property => {
    // Price range filter
    if (property.price < filters.priceRange[0] || property.price > filters.priceRange[1]) {
      return false;
    }
    
    // Lot size filter
    if (property.lotSize < filters.lotSizeRange[0] || property.lotSize > filters.lotSizeRange[1]) {
      return false;
    }
    
    // Zoning filter
    if (filters.zoning.length > 0 && !filters.zoning.includes(property.zoning)) {
      return false;
    }
    
    // Features filter
    if (filters.features.length > 0) {
      const hasFeature = filters.features.some(feature => property.features.includes(feature));
      if (!hasFeature) return false;
    }
    
    // Location filter (simple text match)
    if (filters.location && !property.address.toLowerCase().includes(filters.location.toLowerCase())) {
      return false;
    }
    
    return true;
  });
};

export const propertyApi = {
  // Search properties with filters
  async searchProperties(filters: PropertyFilters): Promise<Property[]> {
    try {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        return generateMockProperties(filters);
      }
      
      // Production API call
      const response = await apiClient.post('/api/v1/properties/search', {
        filters,
      });
      
      return response.data.properties;
    } catch (error) {
      console.error('Error searching properties:', error);
      throw error;
    }
  },

  // Get detailed property information
  async getPropertyDetails(propertyId: string): Promise<Property> {
    try {
      const response = await apiClient.get(`/api/v1/properties/${propertyId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching property details:', error);
      throw error;
    }
  },

  // Get comparable properties
  async getPropertyComparables(propertyId: string): Promise<Property[]> {
    try {
      const response = await apiClient.get(`/api/v1/properties/${propertyId}/comparables`);
      return response.data.comparables;
    } catch (error) {
      console.error('Error fetching property comparables:', error);
      throw error;
    }
  },

  // Get property price history
  async getPropertyHistory(propertyId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/api/v1/properties/${propertyId}/history`);
      return response.data;
    } catch (error) {
      console.error('Error fetching property history:', error);
      throw error;
    }
  },

  // Get market analytics for a location
  async getMarketAnalytics(location: string): Promise<any> {
    try {
      const response = await apiClient.get(`/api/v1/market/analytics`, {
        params: { location },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching market analytics:', error);
      throw error;
    }
  },

  // Add property to watch list
  async addToWatchList(propertyId: string): Promise<void> {
    try {
      await apiClient.post(`/api/v1/watchlist`, { propertyId });
    } catch (error) {
      console.error('Error adding to watch list:', error);
      throw error;
    }
  },

  // Remove property from watch list
  async removeFromWatchList(propertyId: string): Promise<void> {
    try {
      await apiClient.delete(`/api/v1/watchlist/${propertyId}`);
    } catch (error) {
      console.error('Error removing from watch list:', error);
      throw error;
    }
  },

  // Get watch list
  async getWatchList(): Promise<Property[]> {
    try {
      const response = await apiClient.get('/api/v1/watchlist');
      return response.data.properties;
    } catch (error) {
      console.error('Error fetching watch list:', error);
      throw error;
    }
  },
};
