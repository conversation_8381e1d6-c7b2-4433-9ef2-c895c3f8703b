'use client';

import Link from 'next/link';
import {
  MagnifyingGlassIcon,
  MapIcon,
  PlusIcon,
  DocumentArrowDownIcon,
  ChartBarIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';

const quickActions = [
  {
    name: 'New Search',
    description: 'Start a new property search',
    href: '/search',
    icon: MagnifyingGlassIcon,
    color: 'bg-primary-500 hover:bg-primary-600',
  },
  {
    name: 'Map View',
    description: 'Explore properties on map',
    href: '/map',
    icon: MapIcon,
    color: 'bg-success-500 hover:bg-success-600',
  },
  {
    name: 'Add Property',
    description: 'Add property to watch list',
    href: '/watchlist/add',
    icon: PlusIcon,
    color: 'bg-warning-500 hover:bg-warning-600',
  },
  {
    name: 'Export Data',
    description: 'Download property reports',
    href: '/reports/export',
    icon: DocumentArrowDownIcon,
    color: 'bg-purple-500 hover:bg-purple-600',
  },
  {
    name: 'Analytics',
    description: 'View market analytics',
    href: '/analytics',
    icon: ChartBarIcon,
    color: 'bg-indigo-500 hover:bg-indigo-600',
  },
  {
    name: 'Commercial Analysis',
    description: 'Analyze commercial potential',
    href: '/analyzer',
    icon: BuildingOfficeIcon,
    color: 'bg-pink-500 hover:bg-pink-600',
  },
];

export function QuickActions() {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        <p className="text-sm text-gray-500">Frequently used tools and features</p>
      </div>
      <div className="card-body">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              href={action.href}
              className="group relative flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200"
            >
              <div className={`p-3 rounded-lg ${action.color} transition-colors duration-200`}>
                <action.icon className="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              <div className="mt-3 text-center">
                <h4 className="text-sm font-medium text-gray-900 group-hover:text-gray-700">
                  {action.name}
                </h4>
                <p className="text-xs text-gray-500 mt-1">{action.description}</p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
